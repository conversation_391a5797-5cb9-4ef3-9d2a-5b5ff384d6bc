# 宝可梦精灵图下载器

这个脚本用于从PokemonDB网站下载第1至第5世代宝可梦的动态精灵图。

## 系统要求

- Python 3.6 或更高版本
- 所需包: `requests`

## 安装步骤

1. 确保您的系统已安装Python
2. 安装所需的包:

```
pip install requests --no-proxy
```

如果上述命令出错，可以尝试以下方法：

```
# 临时禁用代理
set HTTP_PROXY=
set HTTPS_PROXY=

# 然后安装
pip install requests
```

或者使用国内镜像源：

```
pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 使用方法

1. 运行脚本:

```
python pokemon_scraper.py
```

2. 脚本将会:
   - 为每个世代创建目录(gen1, gen2, gen3, gen4, gen5)
   - 下载第1至第5世代所有宝可梦的动态精灵图
   - 使用顺序编号保存图片(001, 002, 等)
   - 文件命名格式: `{编号}-{宝可梦ID}.gif`

## 注意事项

- 脚本包含下载延迟，以避免对服务器造成过大负担
- 如果下载失败，脚本会报告错误但会继续下载其他宝可梦
- 由于宝可梦数量较多且设置了下载间隔，完整下载可能需要一些时间
- 脚本已禁用代理设置，解决了代理连接问题

## 验证下载

如果您想验证下载的图片是否有效，可以运行验证脚本:

```
pip install pillow
python verify_downloads.py
```

## 简便方法

Windows用户可以直接双击`run_downloader.bat`文件运行下载器，它会自动完成所有步骤。

## 版权信息

- 所有宝可梦精灵图来自[PokemonDB](https://pokemondb.net/sprites)
- 宝可梦(Pokémon)是任天堂(Nintendo)、Game Freak和Creatures Inc.的商标