import os
import requests
import time

# 禁用代理设置
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

# Create directory for saving images if it doesn't exist
def create_directories():
    for gen in range(1, 6):
        os.makedirs(f"gen{gen}", exist_ok=True)

# Function to download image
def download_image(url, file_path):
    try:
        # 设置请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 禁用代理
        proxies = {
            'http': None,
            'https': None
        }
        
        response = requests.get(url, stream=True, headers=headers, proxies=proxies, timeout=10)
        if response.status_code == 200:
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            return True
        else:
            print(f"失败: {url}, 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"下载错误 {url}: {e}")
        return False

# Hardcoded Pokémon data by generation
def get_pokemon_data():
    # Generation 1 (1-151)
    gen1 = [
        "bulbasaur", "ivysaur", "venusaur", "charmander", "charmeleon", "charizard",
        "squirtle", "wartortle", "blastoise", "caterpie", "metapod", "butterfree",
        "weedle", "kakuna", "beedrill", "pidgey", "pidgeotto", "pidgeot",
        "rattata", "raticate", "spearow", "fearow", "ekans", "arbok",
        "pikachu", "raichu", "sandshrew", "sandslash", "nidoran-f", "nidorina",
        "nidoqueen", "nidoran-m", "nidorino", "nidoking", "clefairy", "clefable",
        "vulpix", "ninetales", "jigglypuff", "wigglytuff", "zubat", "golbat",
        "oddish", "gloom", "vileplume", "paras", "parasect", "venonat",
        "venomoth", "diglett", "dugtrio", "meowth", "persian", "psyduck",
        "golduck", "mankey", "primeape", "growlithe", "arcanine", "poliwag",
        "poliwhirl", "poliwrath", "abra", "kadabra", "alakazam", "machop",
        "machoke", "machamp", "bellsprout", "weepinbell", "victreebel", "tentacool",
        "tentacruel", "geodude", "graveler", "golem", "ponyta", "rapidash",
        "slowpoke", "slowbro", "magnemite", "magneton", "farfetchd", "doduo",
        "dodrio", "seel", "dewgong", "grimer", "muk", "shellder",
        "cloyster", "gastly", "haunter", "gengar", "onix", "drowzee",
        "hypno", "krabby", "kingler", "voltorb", "electrode", "exeggcute",
        "exeggutor", "cubone", "marowak", "hitmonlee", "hitmonchan", "lickitung",
        "koffing", "weezing", "rhyhorn", "rhydon", "chansey", "tangela",
        "kangaskhan", "horsea", "seadra", "goldeen", "seaking", "staryu",
        "starmie", "mr-mime", "scyther", "jynx", "electabuzz", "magmar",
        "pinsir", "tauros", "magikarp", "gyarados", "lapras", "ditto",
        "eevee", "vaporeon", "jolteon", "flareon", "porygon", "omanyte",
        "omastar", "kabuto", "kabutops", "aerodactyl", "snorlax", "articuno",
        "zapdos", "moltres", "dratini", "dragonair", "dragonite", "mewtwo", "mew"
    ]
    
    # Generation 2 (152-251)
    gen2 = [
        "chikorita", "bayleef", "meganium", "cyndaquil", "quilava", "typhlosion",
        "totodile", "croconaw", "feraligatr", "sentret", "furret", "hoothoot",
        "noctowl", "ledyba", "ledian", "spinarak", "ariados", "crobat",
        "chinchou", "lanturn", "pichu", "cleffa", "igglybuff", "togepi",
        "togetic", "natu", "xatu", "mareep", "flaaffy", "ampharos",
        "bellossom", "marill", "azumarill", "sudowoodo", "politoed", "hoppip",
        "skiploom", "jumpluff", "aipom", "sunkern", "sunflora", "yanma",
        "wooper", "quagsire", "espeon", "umbreon", "murkrow", "slowking",
        "misdreavus", "unown", "wobbuffet", "girafarig", "pineco", "forretress",
        "dunsparce", "gligar", "steelix", "snubbull", "granbull", "qwilfish",
        "scizor", "shuckle", "heracross", "sneasel", "teddiursa", "ursaring",
        "slugma", "magcargo", "swinub", "piloswine", "corsola", "remoraid",
        "octillery", "delibird", "mantine", "skarmory", "houndour", "houndoom",
        "kingdra", "phanpy", "donphan", "porygon2", "stantler", "smeargle",
        "tyrogue", "hitmontop", "smoochum", "elekid", "magby", "miltank",
        "blissey", "raikou", "entei", "suicune", "larvitar", "pupitar",
        "tyranitar", "lugia", "ho-oh", "celebi"
    ]
    
    # Generation 3 (252-386)
    gen3 = [
        "treecko", "grovyle", "sceptile", "torchic", "combusken", "blaziken",
        "mudkip", "marshtomp", "swampert", "poochyena", "mightyena", "zigzagoon",
        "linoone", "wurmple", "silcoon", "beautifly", "cascoon", "dustox",
        "lotad", "lombre", "ludicolo", "seedot", "nuzleaf", "shiftry",
        "taillow", "swellow", "wingull", "pelipper", "ralts", "kirlia",
        "gardevoir", "surskit", "masquerain", "shroomish", "breloom", "slakoth",
        "vigoroth", "slaking", "nincada", "ninjask", "shedinja", "whismur",
        "loudred", "exploud", "makuhita", "hariyama", "azurill", "nosepass",
        "skitty", "delcatty", "sableye", "mawile", "aron", "lairon",
        "aggron", "meditite", "medicham", "electrike", "manectric", "plusle",
        "minun", "volbeat", "illumise", "roselia", "gulpin", "swalot",
        "carvanha", "sharpedo", "wailmer", "wailord", "numel", "camerupt",
        "torkoal", "spoink", "grumpig", "spinda", "trapinch", "vibrava",
        "flygon", "cacnea", "cacturne", "swablu", "altaria", "zangoose",
        "seviper", "lunatone", "solrock", "barboach", "whiscash", "corphish",
        "crawdaunt", "baltoy", "claydol", "lileep", "cradily", "anorith",
        "armaldo", "feebas", "milotic", "castform", "kecleon", "shuppet",
        "banette", "duskull", "dusclops", "tropius", "chimecho", "absol",
        "wynaut", "snorunt", "glalie", "spheal", "sealeo", "walrein",
        "clamperl", "huntail", "gorebyss", "relicanth", "luvdisc", "bagon",
        "shelgon", "salamence", "beldum", "metang", "metagross", "regirock",
        "regice", "registeel", "latias", "latios", "kyogre", "groudon",
        "rayquaza", "jirachi", "deoxys"
    ]
    
    # Generation 4 (387-493)
    gen4 = [
        "turtwig", "grotle", "torterra", "chimchar", "monferno", "infernape",
        "piplup", "prinplup", "empoleon", "starly", "staravia", "staraptor",
        "bidoof", "bibarel", "kricketot", "kricketune", "shinx", "luxio",
        "luxray", "budew", "roserade", "cranidos", "rampardos", "shieldon",
        "bastiodon", "burmy", "wormadam", "mothim", "combee", "vespiquen",
        "pachirisu", "buizel", "floatzel", "cherubi", "cherrim", "shellos",
        "gastrodon", "ambipom", "drifloon", "drifblim", "buneary", "lopunny",
        "mismagius", "honchkrow", "glameow", "purugly", "chingling", "stunky",
        "skuntank", "bronzor", "bronzong", "bonsly", "mime-jr", "happiny",
        "chatot", "spiritomb", "gible", "gabite", "garchomp", "munchlax",
        "riolu", "lucario", "hippopotas", "hippowdon", "skorupi", "drapion",
        "croagunk", "toxicroak", "carnivine", "finneon", "lumineon", "mantyke",
        "snover", "abomasnow", "weavile", "magnezone", "lickilicky", "rhyperior",
        "tangrowth", "electivire", "magmortar", "togekiss", "yanmega", "leafeon",
        "glaceon", "gliscor", "mamoswine", "porygon-z", "gallade", "probopass",
        "dusknoir", "froslass", "rotom", "uxie", "mesprit", "azelf",
        "dialga", "palkia", "heatran", "regigigas", "giratina", "cresselia",
        "phione", "manaphy", "darkrai", "shaymin", "arceus"
    ]
    
    # Generation 5 (494-649)
    gen5 = [
        "victini", "snivy", "servine", "serperior", "tepig", "pignite",
        "emboar", "oshawott", "dewott", "samurott", "patrat", "watchog",
        "lillipup", "herdier", "stoutland", "purrloin", "liepard", "pansage",
        "simisage", "pansear", "simisear", "panpour", "simipour", "munna",
        "musharna", "pidove", "tranquill", "unfezant", "blitzle", "zebstrika",
        "roggenrola", "boldore", "gigalith", "woobat", "swoobat", "drilbur",
        "excadrill", "audino", "timburr", "gurdurr", "conkeldurr", "tympole",
        "palpitoad", "seismitoad", "throh", "sawk", "sewaddle", "swadloon",
        "leavanny", "venipede", "whirlipede", "scolipede", "cottonee", "whimsicott",
        "petilil", "lilligant", "basculin", "sandile", "krokorok", "krookodile",
        "darumaka", "darmanitan", "maractus", "dwebble", "crustle", "scraggy",
        "scrafty", "sigilyph", "yamask", "cofagrigus", "tirtouga", "carracosta",
        "archen", "archeops", "trubbish", "garbodor", "zorua", "zoroark",
        "minccino", "cinccino", "gothita", "gothorita", "gothitelle", "solosis",
        "duosion", "reuniclus", "ducklett", "swanna", "vanillite", "vanillish",
        "vanilluxe", "deerling", "sawsbuck", "emolga", "karrablast", "escavalier",
        "foongus", "amoonguss", "frillish", "jellicent", "alomomola", "joltik",
        "galvantula", "ferroseed", "ferrothorn", "klink", "klang", "klinklang",
        "tynamo", "eelektrik", "eelektross", "elgyem", "beheeyem", "litwick",
        "lampent", "chandelure", "axew", "fraxure", "haxorus", "cubchoo",
        "beartic", "cryogonal", "shelmet", "accelgor", "stunfisk", "mienfoo",
        "mienshao", "druddigon", "golett", "golurk", "pawniard", "bisharp",
        "bouffalant", "rufflet", "braviary", "vullaby", "mandibuzz", "heatmor",
        "durant", "deino", "zweilous", "hydreigon", "larvesta", "volcarona",
        "cobalion", "terrakion", "virizion", "tornadus", "thundurus", "reshiram",
        "zekrom", "landorus", "kyurem", "keldeo", "meloetta", "genesect"
    ]
    
    return {
        1: [(f"宝可梦 {i+1}", name) for i, name in enumerate(gen1)],
        2: [(f"宝可梦 {i+152}", name) for i, name in enumerate(gen2)],
        3: [(f"宝可梦 {i+252}", name) for i, name in enumerate(gen3)],
        4: [(f"宝可梦 {i+387}", name) for i, name in enumerate(gen4)],
        5: [(f"宝可梦 {i+494}", name) for i, name in enumerate(gen5)]
    }

# Main function
def main():
    create_directories()
    
    # Get Pokémon data
    print("获取宝可梦数据...")
    pokemon_data = get_pokemon_data()
    
    # Count total Pokémon for numbering
    total_count = 1
    
    # Process each generation
    for gen in range(1, 6):
        print(f"\n处理第{gen}世代...")
        gen_pokemon = pokemon_data.get(gen, [])
        
        for pokemon_name, pokemon_id in gen_pokemon:
            # Format number with leading zeros (e.g., 001)
            number = f"{total_count:03d}"
            
            # Create image URL
            image_url = f"https://img.pokemondb.net/sprites/black-white/anim/normal/{pokemon_id}.gif"
            
            # Create file path
            file_path = os.path.join(f"gen{gen}", f"{number}-{pokemon_id}.gif")
            
            print(f"下载 {number} {pokemon_id}...")
            success = download_image(image_url, file_path)
            
            if success:
                print(f"成功下载 {file_path}")
            else:
                print(f"下载失败 {pokemon_id}")
            
            # Increment counter
            total_count += 1
            
            # Be nice to the server
            time.sleep(0.5)

if __name__ == "__main__":
    main()