@echo off
echo 宝可梦精灵图下载器
echo ----------------------

REM 清除代理设置
set HTTP_PROXY=
set HTTPS_PROXY=
set http_proxy=
set https_proxy=

REM 检查是否安装了Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 未安装Python或Python不在PATH环境变量中。
    echo 请从 https://www.python.org/downloads/ 安装Python
    pause
    exit /b
)

REM 安装所需包
echo 正在安装所需包...
pip install requests --no-proxy
if %errorlevel% neq 0 (
    echo 尝试使用国内镜像源...
    pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple
    if %errorlevel% neq 0 (
        echo 安装包失败。
        pause
        exit /b
    )
)

REM 运行下载脚本
echo.
echo 开始下载...
python pokemon_scraper.py

REM 询问是否验证下载文件
echo.
echo 是否要验证下载的文件? (Y/N)
set /p verify=
if /i "%verify%"=="Y" (
    echo 正在安装Pillow用于图片验证...
    pip install pillow --no-proxy
    if %errorlevel% neq 0 (
        echo 尝试使用国内镜像源...
        pip install pillow -i https://pypi.tuna.tsinghua.edu.cn/simple
        if %errorlevel% neq 0 (
            echo 安装Pillow失败。
        ) else (
            echo.
            echo 验证下载...
            python verify_downloads.py
        )
    ) else (
        echo.
        echo 验证下载...
        python verify_downloads.py
    )
)

echo.
echo 处理完成。
pause