import os
import glob
from PIL import Image
import sys

def verify_downloads():
    """
    Verify that all downloaded GIF files are valid images.
    Print a summary of the download results.
    """
    try:
        # Check if PIL is installed
        import PIL
    except ImportError:
        print("PIL (Pillow) is not installed. Please install it with:")
        print("pip install pillow")
        return

    print("Verifying downloaded images...")
    
    total_files = 0
    valid_files = 0
    invalid_files = 0
    
    # Check each generation directory
    for gen in range(1, 6):
        gen_dir = f"gen{gen}"
        
        if not os.path.exists(gen_dir):
            print(f"Directory {gen_dir} not found. No files to verify.")
            continue
        
        # Get all GIF files in the directory
        gif_files = glob.glob(os.path.join(gen_dir, "*.gif"))
        
        print(f"\nGeneration {gen}: Found {len(gif_files)} files")
        total_files += len(gif_files)
        
        # Verify each file
        for gif_file in gif_files:
            try:
                # Try to open the image to verify it's valid
                with Image.open(gif_file) as img:
                    # Get image info to verify it's a valid GIF
                    img.verify()
                    valid_files += 1
            except Exception as e:
                print(f"Invalid image: {gif_file} - {str(e)}")
                invalid_files += 1
    
    # Print summary
    print("\n--- Download Summary ---")
    print(f"Total files: {total_files}")
    print(f"Valid images: {valid_files}")
    print(f"Invalid images: {invalid_files}")
    
    if invalid_files == 0 and total_files > 0:
        print("\nAll downloads are valid!")
    elif total_files == 0:
        print("\nNo files were downloaded.")
    else:
        print(f"\nWarning: {invalid_files} invalid files found.")

if __name__ == "__main__":
    verify_downloads()